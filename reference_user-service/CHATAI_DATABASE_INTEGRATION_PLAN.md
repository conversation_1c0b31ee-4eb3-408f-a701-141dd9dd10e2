# ChatAI-SDK-Clean Database Integration Plan

## 🎯 **Goal: Give ChatAI-SDK-Clean Direct PostgreSQL Access**

This plan outlines how to add PostgreSQL database access to ChatAI-SDK-Clean for better performance and future chat storage capabilities.

## 📋 **Phase 1: Database Connection Setup**

### **1. Add Database Dependencies**

```bash
cd ChatAI-SDK-Clean
npm install pg typeorm reflect-metadata
npm install --save-dev @types/pg
```

### **2. Create Database Configuration**

```javascript
// src/config/database.js
require('dotenv').config();

const databaseConfig = {
  type: 'postgres',
  host: process.env.POSTGRES_HOST || 'localhost',
  port: parseInt(process.env.POSTGRES_PORT, 10) || 5432,
  username: process.env.POSTGRES_USER || 'postgres',
  password: process.env.POSTGRES_PASSWORD || 'postgres',
  database: process.env.POSTGRES_DB || 'abstraxn',
  synchronize: false, // Don't auto-sync, let User-Service handle schema
  logging: process.env.NODE_ENV === 'development',
  entities: [
    // We'll add entities here
  ],
};

module.exports = databaseConfig;
```

### **3. Add Environment Variables**

```env
# Add to ChatAI-SDK-Clean/.env
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=abstraxn
```

## 📋 **Phase 2: Create Database Entities**

### **4. Create ChatAI Entities (Read-Only Initially)**

```javascript
// src/entities/ChatAiDocument.js
const { EntitySchema } = require('typeorm');

const ChatAiDocument = new EntitySchema({
  name: 'ChatAiDocument',
  tableName: 'chat_ai_documents',
  columns: {
    id: {
      type: 'int',
      primary: true,
      generated: true,
    },
    filename: {
      type: 'varchar',
      nullable: false,
    },
    filesize: {
      type: 'int',
      nullable: false,
    },
    contentType: {
      type: 'varchar',
      nullable: false,
    },
    status: {
      type: 'varchar',
      nullable: false,
      default: 'uploading',
    },
    parsedData: {
      type: 'jsonb',
      nullable: true,
    },
    summary: {
      type: 'jsonb',
      nullable: true,
    },
    pageCount: {
      type: 'int',
      nullable: true,
    },
    wordCount: {
      type: 'int',
      nullable: true,
    },
    errorMessage: {
      type: 'text',
      nullable: true,
    },
    indexId: {
      type: 'text',
      nullable: true,
    },
    userId: {
      type: 'varchar',
      nullable: false,
    },
    projectId: {
      type: 'varchar',
      nullable: false,
    },
    createdAt: {
      type: 'timestamp',
      default: () => 'CURRENT_TIMESTAMP',
    },
  },
  relations: {
    project: {
      type: 'many-to-one',
      target: 'ChatAi',
      joinColumn: { name: 'projectId' },
    },
  },
});

module.exports = ChatAiDocument;
```

### **5. Create Chat Message Entity (For Future)**

```javascript
// src/entities/ChatAiMessage.js
const { EntitySchema } = require('typeorm');

const ChatAiMessage = new EntitySchema({
  name: 'ChatAiMessage',
  tableName: 'chat_ai_messages',
  columns: {
    id: {
      type: 'int',
      primary: true,
      generated: true,
    },
    role: {
      type: 'varchar',
      nullable: false, // 'user' or 'assistant'
    },
    content: {
      type: 'text',
      nullable: false,
    },
    metadata: {
      type: 'jsonb',
      nullable: true,
    },
    sessionId: {
      type: 'varchar',
      nullable: true,
    },
    userId: {
      type: 'varchar',
      nullable: false,
    },
    chatAiId: {
      type: 'varchar',
      nullable: false,
    },
    documentId: {
      type: 'int',
      nullable: true,
    },
    createdAt: {
      type: 'timestamp',
      default: () => 'CURRENT_TIMESTAMP',
    },
  },
  relations: {
    chatAi: {
      type: 'many-to-one',
      target: 'ChatAi',
      joinColumn: { name: 'chatAiId' },
    },
    document: {
      type: 'many-to-one',
      target: 'ChatAiDocument',
      joinColumn: { name: 'documentId' },
    },
  },
});

module.exports = ChatAiMessage;
```

## 📋 **Phase 3: Database Service Layer**

### **6. Create Database Service**

```javascript
// src/services/databaseService.js
const { DataSource } = require('typeorm');
const databaseConfig = require('../config/database');
const ChatAiDocument = require('../entities/ChatAiDocument');
const ChatAiMessage = require('../entities/ChatAiMessage');

class DatabaseService {
  constructor() {
    this.dataSource = null;
    this.isConnected = false;
  }

  async initialize() {
    try {
      this.dataSource = new DataSource({
        ...databaseConfig,
        entities: [ChatAiDocument, ChatAiMessage],
      });

      await this.dataSource.initialize();
      this.isConnected = true;
      console.log('✅ Database connection established');
    } catch (error) {
      console.error('❌ Database connection failed:', error.message);
      throw error;
    }
  }

  async updateDocumentStatus(documentId, appId, status, additionalData = {}) {
    try {
      if (!this.isConnected) {
        throw new Error('Database not connected');
      }

      const documentRepo = this.dataSource.getRepository('ChatAiDocument');
      
      // Find document with security check (appId validation)
      const document = await documentRepo
        .createQueryBuilder('document')
        .leftJoin('document.project', 'project')
        .where('document.id = :documentId', { documentId })
        .andWhere('project.appId = :appId', { appId })
        .getOne();

      if (!document) {
        throw new Error('Document not found or access denied');
      }

      // Prepare update data
      const updateFields = {
        status,
        ...additionalData,
      };

      // Update document
      await documentRepo.update(documentId, updateFields);

      console.log(`✅ Document status updated directly: ${documentId} → ${status}`);
      return { success: true, documentId, status };

    } catch (error) {
      console.error(`❌ Database update failed: ${error.message}`);
      throw error;
    }
  }

  async saveChatMessage(messageData) {
    try {
      if (!this.isConnected) {
        throw new Error('Database not connected');
      }

      const messageRepo = this.dataSource.getRepository('ChatAiMessage');
      const message = messageRepo.create(messageData);
      const savedMessage = await messageRepo.save(message);

      console.log(`✅ Chat message saved: ${savedMessage.id}`);
      return savedMessage;

    } catch (error) {
      console.error(`❌ Failed to save chat message: ${error.message}`);
      throw error;
    }
  }

  async getChatHistory(chatAiId, userId, limit = 50) {
    try {
      if (!this.isConnected) {
        throw new Error('Database not connected');
      }

      const messageRepo = this.dataSource.getRepository('ChatAiMessage');
      
      const messages = await messageRepo.find({
        where: {
          chatAiId,
          userId,
        },
        order: {
          createdAt: 'DESC',
        },
        take: limit,
      });

      return messages.reverse(); // Return in chronological order

    } catch (error) {
      console.error(`❌ Failed to get chat history: ${error.message}`);
      throw error;
    }
  }

  async close() {
    if (this.dataSource && this.isConnected) {
      await this.dataSource.destroy();
      this.isConnected = false;
      console.log('✅ Database connection closed');
    }
  }
}

module.exports = new DatabaseService();
```

## 📋 **Phase 4: Update Status Update Logic**

### **7. Replace HTTP Status Updates with Direct Database Updates**

```javascript
// src/routes/vectorProcessing.js - Updated function
const databaseService = require('../services/databaseService');

async function updateDocumentStatusDirect(appId, documentId, status, message, additionalData = {}) {
  try {
    // Try direct database update first
    await databaseService.updateDocumentStatus(documentId, appId, status, {
      ...additionalData,
      lastUpdated: new Date().toISOString(),
    });
    
    console.log(`✅ Status updated directly in database: ${documentId} → ${status}`);
    return true;

  } catch (error) {
    console.warn(`⚠️ Direct database update failed: ${error.message}`);
    
    // Fallback to HTTP API (for backward compatibility)
    console.log(`🔄 Falling back to HTTP API update...`);
    return await notifyUserServiceStatus(appId, documentId, status, message, additionalData);
  }
}

// Replace all notifyUserServiceStatus calls with updateDocumentStatusDirect
```

## 📋 **Phase 5: Initialize Database in Server**

### **8. Update Server Startup**

```javascript
// src/server.js - Add database initialization
const databaseService = require('./services/databaseService');

async function startServer() {
  try {
    // Initialize database connection
    await databaseService.initialize();
    
    // Start Express server
    const PORT = config.port || 3001;
    app.listen(PORT, () => {
      console.log(`🚀 ChatAI SDK Clean running on port ${PORT}`);
      console.log(`📋 Available endpoints:`);
      console.log(`   GET  /api/v1/?apikey=...&query=...  - Main chat endpoint`);
      console.log(`   GET  /health                        - Health check`);
      console.log(`\n🔧 Configuration:`);
      console.log(`   User Service: ${config.userService.url}`);
      console.log(`   Qdrant Vector DB: ${config.qdrant.url}`);
      console.log(`   PostgreSQL: Connected ✅`);
    });

  } catch (error) {
    console.error('❌ Failed to start server:', error.message);
    process.exit(1);
  }
}

// Graceful shutdown
process.on('SIGTERM', async () => {
  console.log('🛑 Received SIGTERM, shutting down gracefully...');
  await databaseService.close();
  process.exit(0);
});

startServer();
```

## 📋 **Phase 6: Future Chat Integration**

### **9. Chat Message Storage (Ready for Future)**

```javascript
// src/routes/chat.js - Future chat endpoint
const express = require('express');
const router = express.Router();
const databaseService = require('../services/databaseService');

router.post('/chat', async (req, res) => {
  try {
    const { message, chatAiId, userId, sessionId } = req.body;

    // Save user message
    await databaseService.saveChatMessage({
      role: 'user',
      content: message,
      chatAiId,
      userId,
      sessionId,
    });

    // Process with vector search and LLM
    const response = await processChat(message, chatAiId);

    // Save assistant response
    await databaseService.saveChatMessage({
      role: 'assistant',
      content: response,
      chatAiId,
      userId,
      sessionId,
    });

    res.json({ response });

  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;
```

## 🚀 **Benefits of This Approach**

### **Immediate Benefits:**
- ✅ **Reliability**: No dependency on User-Service for status updates
- ✅ **Performance**: Direct database access (no HTTP overhead)
- ✅ **Consistency**: Single source of truth for document status

### **Future Benefits:**
- ✅ **Chat Storage**: Ready for chat message persistence
- ✅ **Analytics**: Direct access to chat data for insights
- ✅ **Scalability**: Better performance for high-frequency operations

## 🔒 **Security Considerations**

### **Database Access Control:**
```sql
-- Create dedicated user for ChatAI-SDK-Clean
CREATE USER chatai_sdk WITH PASSWORD 'secure_password';

-- Grant only necessary permissions
GRANT SELECT, INSERT, UPDATE ON chat_ai_documents TO chatai_sdk;
GRANT SELECT, INSERT, UPDATE ON chat_ai_messages TO chatai_sdk;
GRANT SELECT ON chat_ai TO chatai_sdk; -- Read-only for validation

-- Revoke unnecessary permissions
REVOKE DELETE ON chat_ai_documents FROM chatai_sdk;
```

## 📊 **Migration Strategy**

### **Gradual Migration:**
1. **Phase 1**: Add database connection (parallel to HTTP API)
2. **Phase 2**: Switch to direct database updates with HTTP fallback
3. **Phase 3**: Remove HTTP API dependency
4. **Phase 4**: Add chat message storage
5. **Phase 5**: Full chat functionality

This approach ensures zero downtime and easy rollback if needed!
