# ChatAI Integration - Merge Guide

This document contains all the ChatAI-related code and changes that need to be merged into the other developer's codebase.

## 📁 **NEW FILES TO ADD**

### 1. **ChatAI Module Files**
```
src/chatAi/
├── chatAi.controller.ts          # Main ChatAI controller
├── chatAi.service.ts             # ChatAI business logic
├── chatAi.module.ts              # ChatAI module definition
├── dto/
│   └── chatAi.dto.ts             # All ChatAI DTOs
└── entities/
    ├── chatAi.entity.ts          # ChatAI project entity
    ├── document.entity.ts        # Document entity
    ├── message.entity.ts         # Chat message entity
    └── creditUsage.entity.ts     # Credit usage tracking
```

### 2. **Database Migration Files**
```
src/migrations/
├── XXXX-create-chat-ai-tables.ts    # ChatAI database schema
└── XXXX-add-chat-ai-indexes.ts      # Performance indexes
```

## 📋 **EXISTING FILES TO MODIFY**

### 1. **src/app.module.ts**
Add ChatAI module import:
```typescript
import { ChatAiModule } from './chatAi/chatAi.module';

@Module({
  imports: [
    // ... existing imports
    ChatAiModule,
  ],
})
```

### 2. **src/application/entities/application.entity.ts**
Add ChatAI relationship:
```typescript
import { ChatAi } from '../../chatAi/entities/chatAi.entity';

@Entity('applications')
export class Application {
  // ... existing fields

  @OneToMany(() => ChatAi, (chatAi) => chatAi.app)
  chatAis: ChatAi[];
}
```

### 3. **src/user/entities/user.entity.ts**
Add ChatAI relationship:
```typescript
import { ChatAi } from '../../chatAi/entities/chatAi.entity';

@Entity('users')
export class User {
  // ... existing fields

  @OneToMany(() => ChatAi, (chatAi) => chatAi.user)
  chatAis: ChatAi[];
}
```

### 4. **package.json**
Add new dependencies:
```json
{
  "dependencies": {
    "multer": "^1.4.5-lts.1",
    "node-fetch": "^2.6.7"
  },
  "devDependencies": {
    "@types/multer": "^1.4.7",
    "@types/node-fetch": "^2.6.4"
  }
}
```

### 5. **src/CommonMessages/CommonMessages.ts**
Add ChatAI-specific messages:
```typescript
export class AppMessage {
  // ... existing messages

  static ServiceCreated = (service: string) => `${service} created successfully`;
  static ServiceUpdated = (service: string) => `${service} updated successfully`;
  static ServiceDeleted = (service: string) => `${service} deleted successfully`;
  static ServiceFetched = (service: string) => `${service} fetched successfully`;
  static AppNotFound = () => 'Application not found or access denied';
  static DocumentNotFound = () => 'Document not found or access denied';
  static InsufficientCredits = () => 'Insufficient credits for this operation';
}
```

## 🔧 **ENVIRONMENT VARIABLES**

Add to `.env` file:
```env
# ChatAI Configuration
CHATAI_SDK_URL=http://localhost:3001
INTERNAL_API_KEY=chatai-internal-2024

# File Upload Configuration
MAX_FILE_SIZE=20971520  # 20MB in bytes
ALLOWED_FILE_TYPES=pdf,doc,docx,txt,md

# Credit System
DEFAULT_FREE_CREDITS=100
DEFAULT_PRO_CREDITS=10000
```

## 📊 **DATABASE SCHEMA CHANGES**

### New Tables:
1. **chat_ai_projects** - ChatAI project configurations
2. **chat_ai_documents** - Uploaded documents
3. **chat_ai_messages** - Chat conversation history
4. **chat_ai_credit_usage** - Credit usage tracking
5. **chat_ai_api_transactions** - API call logging

### Indexes for Performance:
- Document status and app filtering
- Message timestamp ordering
- Credit usage tracking
- API transaction logging

## 🚀 **KEY FEATURES IMPLEMENTED**

### 1. **Document Management**
- File upload with validation (PDF, DOC, DOCX, TXT, MD)
- Document processing status tracking
- Parsed content storage with pagination
- Document deletion with vector cleanup

### 2. **Chat Functionality**
- Message history storage
- Context-aware responses
- Session management
- Debug mode for admins

### 3. **Credit System**
- Usage tracking per operation
- Subscription-based limits
- Free vs Pro tier management
- API transaction logging

### 4. **API Endpoints**
```
POST   /users/app/chatai/create           # Create ChatAI project
GET    /users/app/chatai/get-chatais      # List projects
GET    /users/app/chatai/get-chatai       # Get single project
PATCH  /users/app/chatai/update-chatai    # Update project
DELETE /users/app/chatai/remove-chatai    # Delete project

POST   /users/app/chatai/upload-document  # Upload document
GET    /users/app/chatai/get-documents    # List documents (no content)
GET    /users/app/chatai/get-document-content  # Get paginated content
PATCH  /users/app/chatai/update-document  # Update document
DELETE /users/app/chatai/remove-document  # Delete document

POST   /users/app/chatai/create-message   # Save chat message
GET    /users/app/chatai/get-messages     # Get chat history
POST   /users/app/chatai/chat-query       # Process chat query

GET    /users/app/key-validator           # API key validation (includes documents)
```

## ⚠️ **IMPORTANT NOTES**

### 1. **Credit Limits Disabled**
Currently commented out in `chatAi.service.ts` line 571-581:
```typescript
// Credit check temporarily disabled
// if (!creditLimits.canUploadDocument) { ... }
```

### 2. **Integration with ChatAI-SDK-Clean**
- Document processing handled by external service
- Vector database operations delegated
- Status updates via internal API calls

### 3. **File Storage**
- Files stored in `uploads/` directory
- Temporary storage during processing
- Cleanup after vector processing

### 4. **Security Features**
- JWT authentication on all endpoints
- File type validation
- Size limits enforcement
- User access control per project

## 🔄 **MIGRATION STEPS**

1. **Copy all ChatAI module files**
2. **Update existing entity relationships**
3. **Add environment variables**
4. **Install new dependencies**
5. **Run database migrations**
6. **Update app.module.ts imports**
7. **Test all endpoints**

## 📝 **TESTING CHECKLIST**

- [ ] ChatAI project CRUD operations
- [ ] Document upload and processing
- [ ] Document content pagination API
- [ ] Chat message storage and retrieval
- [ ] API key validation with documents
- [ ] Credit usage tracking
- [ ] File validation and security
- [ ] User access control
- [ ] Integration with ChatAI-SDK-Clean

---

**Note**: This guide covers only the User-Service changes. The ChatAI-SDK-Clean service remains separate and handles document processing, vector operations, and chat responses.
