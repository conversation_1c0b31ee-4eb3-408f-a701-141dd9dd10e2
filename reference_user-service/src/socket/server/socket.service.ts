import { Injectable, Logger } from '@nestjs/common';
import { Server, Socket } from 'socket.io';
import { AuthService } from '../../auth/auth.service';
import { SocketEvents } from './socket.dto';

@Injectable()
export class SocketService {
  private readonly logger = new Logger(SocketService.name, { timestamp: true });
  constructor(private readonly authService: AuthService) {}

  async handleConnection(socket: Socket, io: Server) {
    const token = socket.handshake.auth.token;
    const request = {
      jwt: token,
    };
    try {
      if (request.jwt) {
        const jwtData = await this.authService.validateToken(request.jwt);
        if (jwtData.error) {
          this.logger.error(
            'Error occurred at socket handshake event :',
            JSON.stringify(jwtData),
          );
          socket.disconnect(); // Disconnect the client
          return;
        }
        const userEmail = jwtData.data.email;
        socket.join(userEmail);
      } else {
        this.logger.warn(
          'Invalid request at initial handshake handleConnection',
        );
      }
    } catch (error) {
      this.logger.debug(
        'Error in catch at socket re_connection event listening',
        error.stack,
      );
    }
    socket.on('re_connection', async (request) => {
      try {
        if (request.jwt) {
          const jwtData = await this.authService.validateToken(request.jwt);
          this.logger.debug(
            'jwtData verified at re_connection event :',
            JSON.stringify(jwtData),
          );
          if (!jwtData.error) {
            const userEmail = jwtData.data.email;
            socket.join(userEmail);
          }
        } else {
          this.logger.warn('Invalid request at re_connection');
        }
      } catch (error) {
        this.logger.debug(
          'Error in catch at socket re_connection event listening',
          error.stack,
        );
      }
    });

    socket.on(SocketEvents.APP_TRANSACTION_WEBHOOK, (data) => {
      this.logger.debug(
        'SocketEvents.APP_TRANSACTION_WEBHOOK',
        JSON.stringify(data),
      );
      this.logger.debug(
        'All available Rooms',
        JSON.stringify(io.sockets.adapter.rooms),
      );
      try {
        io.sockets.in(data.email).emit('app_transaction', data);
      } catch (error) {
        this.logger.error(
          'Error in catch at SocketEvents.APP_TRANSACTION_WEBHOOK',
          JSON.stringify(error),
        );
      }
    });

    socket.on('disconnect', () => {
      this.logger.verbose('Client disconnected:', socket.id);
    });
  }
}
