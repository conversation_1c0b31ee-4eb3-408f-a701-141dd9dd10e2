import { HttpStatus, Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, QueryRunner, Repository } from 'typeorm';
import {
  ChangePasswordDto,
  CreateUserDto,
  SendMailDto,
  ResetPasswordDto,
  VerifyEmailDto,
  CheckUsernameDto,
  GetStartedDto,
  Plan,
} from './dto/user.dto';
import * as bcrypt from 'bcrypt';
import { User } from './entities/user.entity';
import { CommonMessage, UserMessage } from '../CommonMessages/CommonMessages';
import { MailService } from '../mail/mail.service';
import { CryptoUtils } from '../utils/crypto.utils';
import { GatewayService } from '../gateway/gateway.service';
import { BlacklistUsernames, Plans } from '../utils/constants';

@Injectable()
export class UserService {
  private readonly logger = new Logger(UserService.name, { timestamp: true });
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly dataSource: DataSource,
    private readonly mailService: MailService,
    private readonly cryptoUtils: CryptoUtils,
    private readonly gatewayService: GatewayService,
  ) {}

  async findUser(email: string): Promise<User | null> {
    const user: User = await this.userRepository.findOne({
      where: [
        { email: email.toLowerCase() },
        { username: email.toLowerCase() },
      ],
    });
    return user;
  }

  async updateUser(user: User, queryRunner: QueryRunner) {
    try {
      user.updatedAt = new Date();
      const updatedData = await queryRunner.manager.save(user);
      if (!updatedData) {
        await queryRunner.rollbackTransaction();
        this.logger.verbose(
          'rolled back the query runner for updateUser when failed to updateUser',
        );
        return {
          error: true,
          statusCode: HttpStatus.OK,
          message: UserMessage.SaveError,
        };
      }
      return {
        error: false,
        statusCode: HttpStatus.OK,
        message: UserMessage.UpdateSuccess,
      };
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.verbose(
        'rolled back the query runner for updateUser when error in catch',
      );
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }

  private async verificatonCodeGenerator(email: string) {
    const expiryTimestamp = new Date().getTime() + 1 * 60 * 10 * 1000;
    const dataString = JSON.stringify({
      email: email.toLowerCase(),
      expTimestamp: expiryTimestamp,
    });
    return await this.cryptoUtils.encryptData(dataString);
  }

  private async validateToken(token: string, type: string) {
    try {
      const decodedTokenData = await this.cryptoUtils.decryptData(token);
      if (decodedTokenData.error) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: UserMessage.Invalid('token'),
        };
      }
      const parsedToken = JSON.parse(decodedTokenData.data);
      if (
        !parsedToken ||
        !parsedToken.expTimestamp ||
        !new Date(parsedToken.expTimestamp).getTime() ||
        !parsedToken.email
      ) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: UserMessage.Invalid('token'),
        };
      }
      const expiryTimeMillisec = parsedToken.expTimestamp;
      const currentTimeMillisec = new Date().getTime();
      const timeDiffMillsec = expiryTimeMillisec - currentTimeMillisec;

      if (!expiryTimeMillisec || timeDiffMillsec < 0) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: UserMessage.LinkExpired(
            type === 'verifyEmail' ? 'Email verification' : 'Reset password',
          ),
        };
      }
      parsedToken.email = parsedToken.email.toLowerCase();
      const getUserData = await this.findUser(parsedToken.email);
      if (!getUserData) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: UserMessage.Invalid('token'),
        };
      }
      if (type === 'verifyEmail' && getUserData.isEmailVerified) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: UserMessage.AlreadyVerified,
        };
      }
      if (
        (type === 'verifyEmail' && getUserData.verifyToken === null) ||
        (type === 'resetPassword' && getUserData.resetPassToken === null)
      ) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: UserMessage.LinkExpired(
            type === 'verifyEmail' ? 'Email verification' : 'Reset password',
          ),
        };
      }
      if (
        (type === 'verifyEmail' && getUserData.verifyToken != token) ||
        (type === 'resetPassword' && getUserData.resetPassToken != token)
      ) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: UserMessage.Invalid('token'),
        };
      }
      return getUserData;
    } catch (error) {
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }

  async register(createUserDto: CreateUserDto) {
    const queryRunner = this.dataSource.createQueryRunner();
    try {
      // establish real database connection using our new query runner
      await queryRunner.connect();
      // lets now open a new transaction:
      await queryRunner.startTransaction('SERIALIZABLE');
      if (BlacklistUsernames.includes(createUserDto.username.toLowerCase())) {
        await queryRunner.rollbackTransaction();
        this.logger.verbose(
          'rolled back the query runner for register when username blacklisted',
        );
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: CommonMessage.AlreadyCreated,
        };
      }
      const user = await queryRunner.manager.getRepository(User).findOne({
        where: [
          { email: createUserDto.email.toLowerCase() },
          { username: createUserDto.username.toLowerCase() },
        ],
      });
      if (user) {
        await queryRunner.rollbackTransaction();
        this.logger.verbose(
          'rolled back the query runner for register when user already created',
        );
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: CommonMessage.AlreadyCreated,
        };
      }
      const hashedPassword = await bcrypt.hash(createUserDto.password, 10);
      const verificationToken = await this.verificatonCodeGenerator(
        createUserDto.email,
      );
      if (verificationToken.error) {
        await queryRunner.rollbackTransaction();
        this.logger.verbose(
          'rolled back the query runner for register when error in encryption',
        );
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: CommonMessage.InternalError,
        };
      }
      const newUser = new User();
      newUser.firstName = createUserDto.firstName;
      newUser.lastName = createUserDto.lastName;
      newUser.username = createUserDto.username.toLowerCase();
      newUser.email = createUserDto.email.toLowerCase();
      newUser.password = hashedPassword;
      newUser.isActive = true;
      newUser.verifyToken = verificationToken.data;

      const result = await queryRunner.manager.save(newUser);
      if (!result) {
        await queryRunner.rollbackTransaction();
        this.logger.verbose(
          'rolled back the query runner for register when failed to create user',
        );
        return {
          error: true,
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: UserMessage.SaveError,
        };
      }
      const link = `${process.env.FRONTEND_URL}/verify-email?token=${verificationToken.data}`;
      const sentResult = await this.mailService.sendEmail(
        createUserDto.email.toLowerCase(),
        'verifyToken',
        [link],
      );
      if (!sentResult) {
        await queryRunner.rollbackTransaction();
        this.logger.verbose(
          'rolled back the query runner for register when failed sent verification email',
        );
        return {
          error: true,
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: UserMessage.MailError,
        };
      }
      await queryRunner.commitTransaction();
      this.logger.verbose(
        'commited transaction of the query runner for register when record is created successfully',
      );
      return {
        error: false,
        statusCode: HttpStatus.CREATED,
        message: CommonMessage.RegisterationSuccess,
      };
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.verbose(
        'rolled back the query runner for register when error in catch',
      );
      return {
        error: true,
        statusCode: HttpStatus.BAD_REQUEST,
        message: error.message ? error.message : error,
      };
    } finally {
      this.logger.verbose('Releasing the query runner for register...');
      await queryRunner.release();
      this.logger.verbose('register query runner released!');
    }
  }

  async checkUsernameAvail(checkUsernameDto: CheckUsernameDto) {
    const user = await this.userRepository.findOne({
      where: [{ username: checkUsernameDto.username.toLowerCase() }],
    });
    if (
      user ||
      BlacklistUsernames.includes(checkUsernameDto.username.toLowerCase())
    ) {
      return {
        error: true,
        statusCode: HttpStatus.OK,
        message: CommonMessage.AlreadyInUse,
      };
    } else {
      return {
        error: false,
        statusCode: HttpStatus.OK,
        message: CommonMessage.UsernameAvailable,
      };
    }
  }

  async changePassword(userId: number, changePasswordDto: ChangePasswordDto) {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      return {
        error: true,
        statusCode: HttpStatus.BAD_REQUEST,
        message: CommonMessage.UserNotFound,
      };
    }
    const isPasswordValid = await bcrypt.compare(
      changePasswordDto.oldPassword,
      user.password,
    );
    if (!isPasswordValid) {
      return {
        error: true,
        statusCode: HttpStatus.BAD_REQUEST,
        message: CommonMessage.InvalidOldPassword,
      };
    }

    const isOldAndNewSame = await bcrypt.compare(
      changePasswordDto.newPassword,
      user.password,
    );
    if (isOldAndNewSame) {
      return {
        error: true,
        statusCode: HttpStatus.BAD_REQUEST,
        message: CommonMessage.SameOldAndNew,
      };
    }

    // Hash the new password
    const hashedPassword = await bcrypt.hash(changePasswordDto.newPassword, 10);

    // Update the user's password
    user.password = hashedPassword;
    await this.userRepository.save(user);

    return {
      error: false,
      statusCode: HttpStatus.OK,
      message: CommonMessage.PasswordChanged,
    };
  }

  async verifyEmail(verifyEmailDto: VerifyEmailDto) {
    const queryRunner = this.dataSource.createQueryRunner();
    try {
      // establish real database connection using our new query runner
      await queryRunner.connect();
      // lets now open a new transaction:
      await queryRunner.startTransaction();
      const getUserData: User | any = await this.validateToken(
        verifyEmailDto.token,
        'verifyEmail',
      );
      if (getUserData.error) {
        await queryRunner.rollbackTransaction();
        this.logger.verbose(
          'rolled back the query runner for email verification when failed to get user data',
        );
        return getUserData;
      }
      getUserData.isEmailVerified = true;
      getUserData.verifyToken = null;
      const updatedUserData = await this.updateUser(getUserData, queryRunner);
      if (updatedUserData.error) {
        return updatedUserData;
      }
      const addConsumer = await this.gatewayService.createConsumer({
        username: getUserData.username,
        customId: String(getUserData.id),
      });
      if (addConsumer.error) {
        await queryRunner.rollbackTransaction();
        this.logger.verbose(
          'rolled back the query runner for email verification when failed to create consumer',
        );
        return addConsumer;
      }

      await queryRunner.commitTransaction();
      this.logger.verbose(
        'commited transaction of the query runner for email verification when record is updated successfully',
      );
      return {
        error: false,
        statusCode: HttpStatus.OK,
        message: UserMessage.VerifiedSuccess,
      };
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.verbose(
        'rolled back the query runner for email verification when error in catch',
      );
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    } finally {
      this.logger.verbose(
        'Releasing the query runner for email verification...',
      );
      await queryRunner.release();
      this.logger.verbose('register query runner released!');
    }
  }

  async sendMail(sendMailDto: SendMailDto, type: string) {
    const queryRunner = this.dataSource.createQueryRunner();
    try {
      // establish real database connection using our new query runner
      await queryRunner.connect();
      // lets now open a new transaction:
      await queryRunner.startTransaction();
      const user = await this.findUser(sendMailDto.email);
      if (!user) {
        await queryRunner.rollbackTransaction();
        this.logger.verbose(
          'rolled back the query runner for send email when user not exist',
        );
        return {
          error: true,
          statusCode: HttpStatus.NOT_FOUND,
          message: CommonMessage.EmailNotExist,
        };
      }
      if (type === 'verifyToken' && user.isEmailVerified) {
        await queryRunner.rollbackTransaction();
        this.logger.verbose(
          'rolled back the query runner for send email when user already verified',
        );
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: UserMessage.AlreadyVerified,
        };
      }
      if (user[type] != null) {
        const decodedTokenData = await this.cryptoUtils.decryptData(user[type]);
        const parsedToken = JSON.parse(decodedTokenData.data);
        if (
          !parsedToken ||
          !parsedToken.expTimestamp ||
          !new Date(parsedToken.expTimestamp).getTime() ||
          !parsedToken.email
        ) {
          return {
            error: true,
            statusCode: HttpStatus.BAD_REQUEST,
            message: UserMessage.Invalid('token'),
          };
        }
        const expiryTimeMillisec = parsedToken.expTimestamp;
        const currentTimeMillisec = new Date().getTime();
        const timeDiffMillsec = expiryTimeMillisec - currentTimeMillisec;
        if (!expiryTimeMillisec || timeDiffMillsec > 0) {
          const leftMin = Math.round(timeDiffMillsec / 60000);
          const message =
            leftMin < 1
              ? 'less than 1 minute'
              : `${leftMin} minute${leftMin === 1 ? '' : 's'}`;
          return {
            error: true,
            statusCode: HttpStatus.BAD_REQUEST,
            message: UserMessage.OldLinkValid(message),
          };
        }
      }
      const verificationToken = await this.verificatonCodeGenerator(
        sendMailDto.email,
      );
      if (verificationToken.error) {
        await queryRunner.rollbackTransaction();
        this.logger.error(
          'rolled back the query runner for send email when error in encryption',
        );
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: CommonMessage.InternalError,
        };
      }
      user[type] = verificationToken.data;

      const result = await queryRunner.manager.save(user);
      if (!result) {
        await queryRunner.rollbackTransaction();
        this.logger.verbose(
          'rolled back the query runner for send email when failed to save reset token',
        );
        return {
          error: true,
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: CommonMessage.InternalError,
        };
      }
      const link = `${process.env.FRONTEND_URL}/${type === 'verifyToken' ? 'verify-email' : 'create-new-password'}/?token=${verificationToken.data}`;
      const sentResult = await this.mailService.sendEmail(
        sendMailDto.email.toLowerCase(),
        type,
        [link],
      );
      if (!sentResult) {
        await queryRunner.rollbackTransaction();
        this.logger.verbose(
          'rolled back the query runner for send email when error in sending email',
        );
        return {
          error: true,
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: UserMessage.MailError,
        };
      }
      await queryRunner.commitTransaction();
      this.logger.verbose(
        'commited transaction of the query runner for register when record is created successfully',
      );
      return {
        error: false,
        statusCode: HttpStatus.ACCEPTED,
        message: CommonMessage.MailSent(
          type === 'verifyToken' ? 'Email' : 'Forgot password',
        ),
      };
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.verbose(
        'rolled back the query runner for register when error in catch',
      );
      return {
        error: true,
        statusCode: HttpStatus.BAD_REQUEST,
        message: error.message ? error.message : error,
      };
    } finally {
      this.logger.verbose('Releasing the query runner for register...');
      await queryRunner.release();
      this.logger.verbose('register query runner released!');
    }
  }

  async resetPassword(resetPasswordDto: ResetPasswordDto) {
    const queryRunner = this.dataSource.createQueryRunner();
    try {
      // establish real database connection using our new query runner
      await queryRunner.connect();
      // lets now open a new transaction:
      await queryRunner.startTransaction();
      const getUserData: User | any = await this.validateToken(
        resetPasswordDto.token,
        'resetPassword',
      );
      if (getUserData.error) {
        await queryRunner.rollbackTransaction();
        this.logger.verbose(
          'rolled back the query runner for reset pass when failed to get user data',
        );
        return getUserData;
      }
      const hashedPassword = await bcrypt.hash(
        resetPasswordDto.newPassword,
        10,
      );
      getUserData.password = hashedPassword;
      getUserData.resetPassToken = null;
      const updatedUserData = await this.updateUser(getUserData, queryRunner);
      if (updatedUserData.error) {
        return updatedUserData;
      }
      await queryRunner.commitTransaction();
      this.logger.verbose(
        'commited transaction of the query runner for reset pass when record is updated successfully',
      );
      return {
        error: false,
        statusCode: HttpStatus.OK,
        message: UserMessage.ResetSuccess,
      };
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.verbose(
        'rolled back the query runner for reset pass when error in catch',
      );
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    } finally {
      this.logger.verbose('Releasing the query runner for reset pass...');
      await queryRunner.release();
      this.logger.verbose('register query runner released!');
    }
  }

  async getStarted(userId: number, getStartedDto: GetStartedDto) {
    const queryRunner = this.dataSource.createQueryRunner();
    try {
      // establish real database connection using our new query runner
      await queryRunner.connect();
      // lets now open a new transaction:
      await queryRunner.startTransaction();
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        await queryRunner.rollbackTransaction();
        this.logger.verbose(
          'rolled back the query runner for getStarted when failed to get user data',
        );
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: CommonMessage.UserNotFound,
        };
      }
      if (user.plan === getStartedDto.plan) {
        await queryRunner.rollbackTransaction();
        this.logger.verbose(
          'rolled back the query runner for getStarted when already enroll same plan',
        );
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: UserMessage.AlreadyEnrolledPlan,
        };
      }
      user.plan = getStartedDto.plan;
      const updatedUserData = await this.updateUser(user, queryRunner);
      if (updatedUserData.error) {
        return updatedUserData;
      }
      if (getStartedDto.plan != Plan.FREE) {
        await queryRunner.rollbackTransaction();
        this.logger.verbose(
          'rolled back the query runner for getStarted when error in plan selection',
        );
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: UserMessage.InvalidPlan,
        };
      }
      const setConsumerRateLimit =
        await this.gatewayService.setConsumerRateLimit({
          username: user.username,
          // service: Services.paymaster,
          day: Plans.free.limitPerDay,
          second: Plans.free.limitPerSec,
          plan: getStartedDto.plan,
        });
      if (setConsumerRateLimit.error) {
        await queryRunner.rollbackTransaction();
        this.logger.verbose(
          'rolled back the query runner for email getStarted when failed to set consumer limit plan',
        );
        return setConsumerRateLimit;
      }
      this.logger.debug(
        'setConsumerRateLimit response from gateway:::',
        JSON.stringify(setConsumerRateLimit),
      );

      await queryRunner.commitTransaction();
      this.logger.verbose(
        'commited transaction of the query runner for getStarted when record is updated successfully',
      );
      return {
        error: false,
        statusCode: HttpStatus.OK,
        message: UserMessage.EnrolledPlan(getStartedDto.plan),
      };
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.verbose(
        'rolled back the query runner for getStarted when error in catch',
      );
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    } finally {
      this.logger.verbose('Releasing the query runner for getStarted...');
      await queryRunner.release();
      this.logger.verbose('register query runner released!');
    }
  }
}
