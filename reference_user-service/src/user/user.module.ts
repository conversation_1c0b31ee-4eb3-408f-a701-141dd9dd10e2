import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserController } from './user.controller';
import { UserService } from './user.service';
import { User } from './entities/user.entity';
import { AuthModule } from '../auth/auth.module';
import { MailModule } from '../mail/mail.module';
import { CryptoUtils } from '../utils/crypto.utils';
import { GatewayModule } from '../gateway/gateway.module';
import { BundlerModule } from '../bundler/bundler.module';

@Module({
  imports: [
    forwardRef(() => AuthModule),
    TypeOrmModule.forFeature([User]),
    MailModule,
    GatewayModule,
    BundlerModule,
  ],
  controllers: [UserController],
  providers: [UserService, CryptoUtils],
  exports: [UserService],
})
export class UserModule {}
