import { Module, forwardRef } from '@nestjs/common';
import { BundlerService } from './bunlder.service';
import { AuthModule } from '../auth/auth.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from '../user/entities/user.entity';
import { GatewayModule } from '../gateway/gateway.module';
import { ConfigModule } from '@nestjs/config';
import { Bundler } from './entities/bundler.entity';
import { BundlerController } from './bundler.controller';
import { Application } from '../application/entities/application.entity';
import { PaymasterModule } from '../paymaster/paymaster.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    forwardRef(() => AuthModule),
    TypeOrmModule.forFeature([User, Bundler, Application]),
    GatewayModule,
    PaymasterModule,
  ],
  providers: [BundlerService],
  controllers: [BundlerController],
  exports: [BundlerService],
})
export class BundlerModule {}
