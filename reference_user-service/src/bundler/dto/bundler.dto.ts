import {
  IsBoolean,
  IsNotEmpty,
  Is<PERSON>ptional,
  IsString,
  IsUUI<PERSON>,
  Matches,
  <PERSON>Length,
  MinLength,
} from 'class-validator';
import { Transform } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Regexs } from '../../utils/constants';

export class CreateBundlerDto {
  @ApiProperty({
    description: 'Name of the bundler',
    minLength: 3,
    maxLength: 50,
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(3)
  @MaxLength(50)
  @Matches(Regexs.OnlyAlphaNumericWithSpace, {
    message: 'Bundler name must contain only Alphanumeric characters',
  })
  @Transform(({ value }) => value.replace(/\s+/g, ' ').trim()) // Trim whitespace before validation
  name: string;

  @ApiProperty({
    description: 'Id of app',
    required: true,
  })
  @IsNotEmpty()
  @IsUUID()
  appId: string;
}

export class UpdateBundlerDto {
  @ApiPropertyOptional({
    description: 'ID of the app',
  })
  @IsNotEmpty()
  @IsUUID()
  appId: string;

  @ApiProperty({
    description: 'Name of the bundler',
    minLength: 3,
    maxLength: 50,
  })
  @IsOptional()
  @IsNotEmpty()
  @IsString()
  @MinLength(3)
  @MaxLength(50)
  @Matches(Regexs.OnlyAlphaNumericWithSpace, {
    message: 'Bundler name must contain only Alphanumeric characters',
  })
  @Transform(({ value }) => value.replace(/\s+/g, ' ').trim()) // Trim whitespace before validation
  bundlerName: string;

  @ApiPropertyOptional({
    description: 'Flag indicating if the application is active',
  })
  @IsOptional()
  @IsBoolean()
  @IsNotEmpty()
  isActive?: boolean;
}

export class FetchSingleBundlerDto {
  @ApiProperty({
    description: 'Id of app',
    required: true,
  })
  @IsNotEmpty()
  @IsUUID()
  appId: string;
}

export class FetchAllBundlerDto {
  @ApiProperty({
    description: 'Page number',
    required: false,
    default: 1,
  })
  page?: number;

  @ApiProperty({
    description: 'Limit per page',
    required: false,
    default: 10,
  })
  limit?: number;

  @ApiProperty({
    description: 'Search keyword',
    required: false,
  })
  s?: string;
}
