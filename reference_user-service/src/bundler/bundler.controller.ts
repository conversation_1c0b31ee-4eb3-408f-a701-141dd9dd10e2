import {
  Body,
  Controller,
  Delete,
  Get,
  Patch,
  Post,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { BundlerService } from './bunlder.service';
import { User } from '../user/entities/user.entity';
import {
  CreateBundlerDto,
  FetchAllBundlerDto,
  FetchSingleBundlerDto,
  UpdateBundlerDto,
} from './dto/bundler.dto';
import { RemovePaymasterDto } from '../paymaster/dto/paymaster.dto';
import { PaymasterService } from '../paymaster/paymaster.service';

@ApiTags('Bundler')
@Controller('users/app/bundler')
export class BundlerController {
  constructor(
    private readonly bundlerService: BundlerService,
    private readonly paymasterService: PaymasterService,
  ) {}

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Post('setup-bundler')
  async setupBundler(
    @Req() req: { user: User },
    @Body() createBundlerDto: CreateBundlerDto,
  ) {
    const userId = req.user.id;
    return await this.bundlerService.setupBundler(userId, createBundlerDto);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Patch('update-bundler')
  async updateBundler(
    @Req() req: { user: User },
    @Body() updateBundlerDto: UpdateBundlerDto,
  ) {
    const userId = req.user.id;
    return await this.bundlerService.updateBundler(userId, updateBundlerDto);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Get('get-single-bundler')
  async getSingleApp(
    @Req() req: { user: User },
    @Query() query: FetchSingleBundlerDto,
  ) {
    const userId = req.user.id;
    return await this.bundlerService.getSingleBundler(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Get('get-all-bundlers')
  async getAllApp(
    @Req() req: { user: User },
    @Query() query: FetchAllBundlerDto,
  ) {
    const userId = req.user.id;
    return await this.bundlerService.getAllBundlers(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Delete('delete-bundler')
  async deleteApiKey(
    @Req() req: { user: User },
    @Query() payload: RemovePaymasterDto,
  ) {
    const userId = req.user.id;
    return this.paymasterService.deleteApplicationService(userId, payload);
  }
}
