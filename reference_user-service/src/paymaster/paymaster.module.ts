import { Module, forwardRef } from '@nestjs/common';
import { PaymasterService } from './paymaster.service';
import { PaymasterController } from './paymaster.controller';
import { AuthModule } from '../auth/auth.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from '../user/entities/user.entity';
import { Paymaster } from './entities/paymaster.entity';
import { GatewayModule } from '../gateway/gateway.module';
import { TransactionModule } from '../transaction/transaction.module';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { ConfigModule } from '@nestjs/config';
import { AbiService } from '../utils/abi.service';
import { Application } from '../application/entities/application.entity';
import { OriginsModule } from '../origins/origins.module';
import { PodCoordinatorModule } from '../pod-coordinator/pod-coordinator.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    forwardRef(() => AuthModule),
    TypeOrmModule.forFeature([Application, Paymaster, User]),
    GatewayModule,
    TransactionModule,
    ClientsModule.register([
      {
        name: 'PAYMASTER_DEPOSIT_SERVICE',
        transport: Transport.RMQ,
        options: {
          urls: [process.env.RMQ_URL],
          queue: 'paymaster_deposit_q',
          queueOptions: {
            durable: true,
            autoDelete: true,
          },
        },
      },
    ]),
    OriginsModule,
    PodCoordinatorModule,
  ],
  providers: [PaymasterService, AbiService],
  controllers: [PaymasterController],
  exports: [PaymasterService, AbiService],
})
export class PaymasterModule {}
