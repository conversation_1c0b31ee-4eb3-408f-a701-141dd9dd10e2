import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AppRelayer } from '../appRelayer/entities/appRelayer.entity';
import { MailService } from 'src/mail/mail.service';
import { SchedulerRegistry } from '@nestjs/schedule';
import { DEFAULT_THRESHOLD_MAP } from 'src/utils/constants';
import { Application } from '../application/entities/application.entity';
import { BasePodService } from '../common/base-pod.service';
import { PodCoordinatorService } from '../pod-coordinator/pod-coordinator.service';

@Injectable()
export class NotificationService extends BasePodService {
  private readonly logger = new Logger(NotificationService.name);
  private readonly NOTIFY_INTERVAL_HOURS = 12;
  private cronJob: any;

  constructor(
    @InjectRepository(Application)
    private readonly applicationRepository: Repository<Application>,
    @InjectRepository(AppRelayer)
    private readonly appRelayerRepository: Repository<AppRelayer>,
    private readonly mailService: MailService,
    protected readonly podCoordinatorService: PodCoordinatorService,
    private schedulerRegistry: SchedulerRegistry,
  ) {
    super(podCoordinatorService);
    this.setupLeadershipHandling();
  }

  private setupLeadershipHandling() {
    // Set up watcher for leader status changes
    this.podCoordinatorService.onLeadershipChange((isLeader: boolean) => {
      if (isLeader) {
        this.startCronJob();
      } else {
        this.stopCronJob();
      }
    });

    // Initial setup based on current leader status
    if (this.isLeaderPod()) {
      this.startCronJob();
    }
  }

  private startCronJob() {
    if (this.cronJob) {
      return; // Job already running
    }

    this.cronJob = setInterval(
      async () => {
        this.logger.log('Running balance check on leader pod');
        await this.checkAndNotifyLowBalance();
      },
      10 * 60 * 1000, // 10 minutes
    );

    this.schedulerRegistry.addInterval('checkRelayerBalances', this.cronJob);
  }

  private stopCronJob() {
    if (this.cronJob) {
      clearInterval(this.cronJob);
      this.schedulerRegistry.deleteInterval('checkRelayerBalances');
      this.cronJob = null;
      this.logger.debug('Stopped balance check on non-leader pod');
    }
  }

  async checkAndNotifyLowBalance() {
    const sixHoursAgo = new Date();
    sixHoursAgo.setHours(sixHoursAgo.getHours() - this.NOTIFY_INTERVAL_HOURS);

    // Get all active relayers with notifications enabled that haven't been notified recently
    const relayers = await this.appRelayerRepository
      .createQueryBuilder('relayer')
      .leftJoinAndSelect('relayer.app', 'app')
      .leftJoinAndSelect('app.user', 'user')
      .where(
        '(relayer.lastNotifiedAt IS NULL OR relayer.lastNotifiedAt < :sixHoursAgo) AND relayer.createdAt < :sixHoursAgo AND relayer.notificationsEnabled = :notificationsEnabled',
        { sixHoursAgo, notificationsEnabled: true },
      )
      .getMany();

    if (relayers.length === 0) {
      this.logger.verbose('No relayers found that need notification.');
      return;
    }

    // Group all relayers by user email
    const userRelayersMap = new Map<
      string,
      {
        email: string;
        allRelayers: any[];
        lowBalanceRelayers: {
          appName: string;
          relayerName: string;
          balance: string;
          threshold: string;
        }[];
      }
    >();

    // First, group all relayers by user
    for (const relayer of relayers) {
      // Use notificationEmail if it exists, otherwise use user's email
      const emailToUse = relayer.notificationEmail || relayer.app?.user?.email;

      if (emailToUse) {
        if (!userRelayersMap.has(emailToUse)) {
          userRelayersMap.set(emailToUse, {
            email: emailToUse,
            allRelayers: [],
            lowBalanceRelayers: [],
          });
        }

        userRelayersMap.get(emailToUse)!.allRelayers.push(relayer);

        const chainId = relayer.app?.chainId;
        const defaultThreshold =
          DEFAULT_THRESHOLD_MAP[chainId] ?? DEFAULT_THRESHOLD_MAP.default;

        const threshold =
          parseFloat(relayer.threshold) > 0
            ? parseFloat(relayer.threshold)
            : defaultThreshold;

        const balance = parseFloat(relayer.balance);

        if (balance < threshold) {
          const formattedThreshold =
            parseFloat(relayer.threshold) > 0
              ? relayer.threshold
              : defaultThreshold.toString();

          userRelayersMap.get(emailToUse)!.lowBalanceRelayers.push({
            appName: relayer.app.appName,
            relayerName: relayer.relayerName,
            balance: relayer.balance,
            threshold: formattedThreshold,
          });
        }
      }
    }

    // Filter to only users who have at least one low-balance relayer
    const usersToNotify = Array.from(userRelayersMap.values()).filter(
      (user) => user.lowBalanceRelayers.length > 0,
    );

    if (usersToNotify.length === 0) {
      this.logger.verbose('No users have relayers with low balance.');
      return;
    }

    // Send a single email per user with all their low-balance relayers
    const emailResults = await Promise.allSettled(
      usersToNotify.map(({ email, lowBalanceRelayers }) =>
        this.notifyUser(email, lowBalanceRelayers),
      ),
    );

    // Update lastNotifiedAt for ALL relayers belonging to successfully notified users
    emailResults.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        const userEmail = usersToNotify[index].email;
        const allUserRelayers = userRelayersMap.get(userEmail)!.allRelayers;

        // Set the same notification timestamp for all relayers of this user
        const now = new Date();
        allUserRelayers.forEach((relayer) => {
          relayer.lastNotifiedAt = now;
        });
      }
    });

    // Save all updated relayers
    await this.appRelayerRepository.save(relayers);

    this.logger.verbose(
      `Sent low balance notifications to ${usersToNotify.length} users.`,
    );
  }

  private async notifyUser(
    email: string,
    relayers: {
      appName: string;
      relayerName: string;
      balance: string;
      threshold: string;
    }[],
  ) {
    try {
      const sentResult = await this.mailService.sendEmail(
        email,
        'lowGasTankBalance',
        relayers,
      );

      if (!sentResult) {
        this.logger.error(`Failed to send email to ${email}`);
      } else {
        this.logger.verbose(`📧 Email sent to ${email} about low balance.`);
      }
    } catch (error) {
      this.logger.error(
        `Failed to send email to ${email} with error: ${error}`,
      );
    }
  }
}
