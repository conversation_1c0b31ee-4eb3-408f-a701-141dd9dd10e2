import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Application } from '../application/entities/application.entity';
import { AppRelayer } from '../appRelayer/entities/appRelayer.entity';
import { NotificationService } from './notification.service';
import { MailService } from '../mail/mail.service';
import { PodCoordinatorModule } from '../pod-coordinator/pod-coordinator.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Application, AppRelayer]),
    PodCoordinatorModule,
  ],
  providers: [NotificationService, MailService],
  exports: [NotificationService],
})
export class NotificationModule {}
