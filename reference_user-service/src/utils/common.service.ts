import { ChainId } from './utils.networks';

export async function validateChainId(chainId: number): Promise<string> {
  const validChainIds = Object.keys(ChainId).find(
    (key) => ChainId[key] === Number(chainId),
  );
  return validChainIds;
}

export const paginate = (page = 1, limit = 10) => {
  page = page ? Number(page) : 1;
  limit = limit ? Number(limit) : 10;
  return (page - 1) * limit;
};

export const compareArrays = (array1, array2) => {
  // Check if arrays are of equal length
  if (array1.length !== array2.length) {
    return false;
  }

  // Convert arrays to sets to remove duplicates
  const set1 = new Set(array1);
  const set2 = new Set(array2);

  // Check if sets have the same size
  if (set1.size !== set2.size) {
    return false;
  }

  // Check if every element in set1 exists in set2
  for (const item of set1) {
    if (!set2.has(item)) {
      return false;
    }
  }

  // If all elements are equal, return true
  return true;
};

function includeArrays(array1: Iterable<unknown>, array2: Iterable<unknown>) {
  const error: any = [];
  const set1 = new Set(array1);
  const set2 = new Set(array2);
  for (const item of set1) {
    if (!set2.has(item) && set2.size) {
      error.push(item);
      return { error, valid: false };
    }
  }
  return { error, valid: true };
}

export function validateContractFunction(
  filter: { [x: string]: { [s: string]: unknown } | ArrayLike<unknown> },
  arr: any[],
) {
  const error: any = [];
  let errorObj;
  const message = (feature) => `Contract method: ${feature} is not whitelisted`;
  if (!arr.length || !arr) {
    // return { error, valid: true };
    return { error: false, message: null };
  }
  for (const iterator in filter) {
    const validate = arr.find(
      (a: { address: string }) =>
        a.address.toLowerCase() == iterator.toLowerCase(),
    );
    if (!validate) {
      errorObj = {
        method: Object.values(filter[iterator]),
        address: iterator,
      };
      error.push({
        contract: iterator,
        methods: Object.values(filter[iterator]),
        text: message(JSON.stringify(errorObj)),
      });
      // return { error, valid: false };
      return { error: true, message: message(JSON.stringify(errorObj)) };
    }

    const list = Object.values(filter[iterator]);
    const isValid = includeArrays(list, validate.whitelistedMethods);
    if (!isValid.valid) {
      errorObj = {
        method: isValid.error,
        address: iterator,
      };
      error.push({
        contract: iterator,
        methods: isValid.error,
        text: message(JSON.stringify(errorObj)),
      });
      // return { error, valid: false };
      return { error: true, message: message(JSON.stringify(errorObj)) };
    }
  }
  // return { error, valid: true };
  return { error: false, message: null };
}

function normalizeOrigin(origin: string) {
  try {
    const url = new URL(origin);
    if (url.hostname.startsWith('www.')) {
      url.hostname = url.hostname.substring(4);
    }
    return { origin: url.origin, href: url.href };
  } catch (error) {
    return null;
  }
}

export function validateUrlFromDatabase(
  origin: string,
  allowedOrigins: string[],
) {
  const normalizedOrigin = normalizeOrigin(origin);
  if (!normalizedOrigin) {
    return false;
  }
  if (!Object.keys(normalizedOrigin).length || !normalizedOrigin) {
    return false;
  }
  return allowedOrigins.some(
    (origin: any) =>
      normalizedOrigin.origin === origin.originUrl ||
      normalizedOrigin.href === origin.originUrl,
  );
}

export function formatDate(timestamp: any) {
  const date = new Date(timestamp);
  const options: any = {
    weekday: 'short',
    month: 'short',
    day: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    timeZoneName: 'short',
    timeZone: 'GMT',
  };
  const formattedDate = date.toLocaleString('en-US', options);
  return formattedDate;
}
