import { ChainId } from './utils.networks';

export const BlacklistUsernames = ['abstraxn'];

export class Regexs {
  public static readonly OnlyAlphabets = /(^[a-zA-Z]+$)/i;
  public static readonly OnlyAlphaNumericWithSpace = /^[a-zA-Z0-9\s]*$/;
  public static readonly AlphanumericWithSpecialCharsAndSpace =
    /^[a-zA-Z0-9\s!@#$%^&*()\-_=+[\]{};:'",.<>/?`~]*$/;
  public static readonly OnlyAlphabetsWithSpace = /^[a-zA-Z]+(?:\s[a-zA-Z]+)*$/;
  public static readonly textWithoutSpace = /^[^\s]+$/;
  public static readonly email = /^[a-zA-Z0-9.+]+@[a-zA-Z0-9]+\.[a-zA-Z]{2,}$/g;
  public static readonly Username = /^[a-zA-Z]+[a-zA-Z0-9-.@_]*$/g;
  public static readonly Password =
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[~!@#$%^&*()_+{}[\]|:;'"<>?,./-])[A-Za-z\d~!@#$%^&*()_+{}[\]|:;'"<>?,./-]{8,20}$/;
  public static readonly Url =
    /^(http:\/\/www\.|https:\/\/www\.|http:\/\/|https:\/\/)?[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/;
}

export const Services = {
  paymaster: 'paymaster-service',
  bundler: 'bundler-service',
};

export const Plans = {
  free: {
    limitPerSec: 10,
    limitPerDay: 10000,
    appsLimit: 5,
  },
};

export const MESSAGE =
  "Welcome to Abstraxn! This request will link your gas tank to our application. It won't initiate any blockchain transactions or result in any fees.";

export const ValidForABi = [
  56, 97, 1, 5, 11155111, 17000, 137, 80001, 80002, 1101, 1442,
];

// export const DEPOSIT_CONTRACT_ADDRESS =
//   '******************************************';

export const DEPOSIT_CONTRACT_ADDRESS =
  '******************************************';

export const BLOCK_DIFF_NUMBER = 1000;

export const DEFAULT_THRESHOLD = 1;

export const DEFAULT_THRESHOLD_MAP: Record<string | number, number> = {
  [ChainId.MAINNET]: 0.01, // Ethereum Mainnet
  [ChainId.POLYGON_MAINNET]: 1, // Polygon
  [ChainId.BSC_MAINNET]: 0.5, // BSC
  default: 1, // fallback
};

export const SUPPORTED_ENTRYPOINTS = {
  EPV_06: '******************************************',
  EPV_07: '******************************************',
  EPV_08: '******************************************',
};

export enum EPVersions {
  EPV_06 = 'EPV_06',
  EPV_07 = 'EPV_07',
  EPV_08 = 'EPV_08',
  // Add more versions here as needed
}

export const DEFAULT_EP_VERSION: EPVersions = EPVersions.EPV_06;

export function getEPVersion(value: string): EPVersions {
  switch (value) {
    case 'EPV_06':
      return EPVersions.EPV_06;
    case 'EPV_07':
      return EPVersions.EPV_07;
    case 'EPV_08':
      return EPVersions.EPV_08;
    default:
      throw new Error('Unsupported EP version');
  }
}

export function getEPVersionString(value: EPVersions): string {
  switch (value) {
    case EPVersions.EPV_06:
      return 'EPV_06';
    case EPVersions.EPV_07:
      return 'EPV_07';
    case EPVersions.EPV_08:
      return 'EPV_08';
    default:
      throw new Error('Unsupported EP version');
  }
}

export interface VerificationGasLimits {
  validateUserOpGas: number;
  validatePaymasterUserOpGas: number;
  postOpGas: number;
}
