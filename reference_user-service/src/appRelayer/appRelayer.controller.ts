import {
  Body,
  Controller,
  Delete,
  Get,
  Patch,
  Post,
  Query,
  Req,
  UseGuards,
  Logger,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { AppRelayerService } from './appRelayer.service';
import {
  CreateSmartContractDto,
  GetAppTransactionDto,
  GetContractAbiDto,
  UpdateSmartContractDto,
  RemoveSmartContractDto,
  ListSmartContractsDto,
  CreateRelayerDto,
  UpdateRelayerDto,
  FetchSingleRelayerDto,
  FetchRelayersDto,
  RemoveRelayerDto,
  UpdateRelayerSettingDto,
  GetAppRelayerTransactionDto,
} from './dto/appRelayer.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { User } from '../user/entities/user.entity';
import { EventPattern, Payload, Ctx, RmqContext } from '@nestjs/microservices';
import { BasePodService } from '../common/base-pod.service';
import { PodCoordinatorService } from '../pod-coordinator/pod-coordinator.service';

@ApiTags('Relayer')
@Controller('users/app/relayer')
export class AppRelayerController extends BasePodService {
  private readonly logger = new Logger(AppRelayerController.name);

  constructor(
    private readonly appRelayerService: AppRelayerService,
    protected readonly podCoordinatorService: PodCoordinatorService,
  ) {
    super(podCoordinatorService);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Post('setup-relayer')
  async setupRelayer(
    @Req() req: { user: User },
    @Body() createRelayerDto: CreateRelayerDto,
  ) {
    const userId = req.user.id;
    return await this.appRelayerService.setupRelayer(userId, createRelayerDto);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Patch('update-relayer')
  async updateApp(
    @Req() req: { user: User },
    @Body() updateRelayerDto: UpdateRelayerDto,
  ) {
    const userId = req.user.id;
    return await this.appRelayerService.updateRelayer(userId, updateRelayerDto);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Patch('update-settings')
  async updateRelayerSetting(
    @Req() req: { user: User },
    @Body() updateRelayerSettingDto: UpdateRelayerSettingDto,
  ) {
    const userId = req.user.id;
    return await this.appRelayerService.updateRelayerSettings(
      userId,
      updateRelayerSettingDto,
    );
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Get('get-single-relayer')
  async getSingleApp(
    @Req() req: { user: User },
    @Query() query: FetchSingleRelayerDto,
  ) {
    const userId = req.user.id;
    return await this.appRelayerService.getSingleRelayer(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Get('get-all-relayers')
  async getAllApp(
    @Req() req: { user: User },
    @Query() query: FetchRelayersDto,
  ) {
    const userId = req.user.id;
    return await this.appRelayerService.getAllRelayers(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Get('get-transactions')
  async getTransactions(
    @Query() query: GetAppTransactionDto,
    @Req() req: { user: User },
  ) {
    const userId = req.user.id;
    return await this.appRelayerService.getAllTransactions(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Get('get-relayer-transactions')
  async getRelayerTransactions(
    @Query() query: GetAppRelayerTransactionDto,
    @Req() req: { user: User },
  ) {
    const userId = req.user.id;
    return await this.appRelayerService.getAllRelayerTransactions(
      userId,
      query,
    );
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Get('get-contract-abi')
  async getContractAbi(
    @Query() query: GetContractAbiDto,
    @Req() req: { user: User },
  ) {
    const userId = req.user.id;
    return await this.appRelayerService.getContractAbi(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Post('add-smart-contract')
  async addSmartContract(
    @Req() req: { user: User },
    @Body() createSmartContractDto: CreateSmartContractDto,
  ) {
    const userId = req.user.id;
    return await this.appRelayerService.createSmartContract(
      userId,
      createSmartContractDto,
    );
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Patch('update-smart-contract')
  async updateSmartContract(
    @Req() req: { user: User },
    @Body() updateSmartContractDto: UpdateSmartContractDto,
  ) {
    const userId = req.user.id;
    return await this.appRelayerService.updateSmartContract(
      userId,
      updateSmartContractDto,
    );
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Post('remove-smart-contract')
  async removeSmartContract(
    @Req() req: { user: User },
    @Body() removeSmartContractDto: RemoveSmartContractDto,
  ) {
    const userId = req.user.id;
    return await this.appRelayerService.removeSmartContract(
      userId,
      removeSmartContractDto,
    );
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Get('get-smart-contracts')
  async getAllSmartContract(
    @Req() req: { user: User },
    @Query() getSmartContractDto: ListSmartContractsDto,
  ) {
    const userId = req.user.id;
    return await this.appRelayerService.getAllSmartContract(
      userId,
      getSmartContractDto,
    );
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Delete('delete-relayer')
  async deleteApiKey(
    @Req() req: { user: User },
    @Query() payload: RemoveRelayerDto,
  ) {
    const userId = req.user.id;
    return this.appRelayerService.deleteApplicationService(userId, payload);
  }

  @EventPattern('update_app_relayer')
  async handleAppRelayerUpdate(
    @Payload() appRelayerData: any,
    @Ctx() context: RmqContext,
  ) {
    const channel = context.getChannelRef();
    const originalMsg = context.getMessage();

    try {
      this.logger.log('Processing app relayer update');
      await this.appRelayerService.updateAppRelayerBalances(appRelayerData);
      channel.ack(originalMsg);
    } catch (error) {
      this.logger.error('Error processing app relayer update:', error);
      channel.nack(originalMsg, false, true);
    }
  }
}
