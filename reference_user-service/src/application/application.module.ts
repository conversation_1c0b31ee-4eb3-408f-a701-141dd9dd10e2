import { Module, forwardRef } from '@nestjs/common';
import { AuthModule } from '../auth/auth.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from '../user/entities/user.entity';
import { GatewayModule } from '../gateway/gateway.module';
import { ConfigModule } from '@nestjs/config';
import { AbiService } from '../utils/abi.service';
import { Application } from './entities/application.entity';
import { ApplicationController } from './application.controller';
import { ApplicationService } from './application.service';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    forwardRef(() => AuthModule),
    TypeOrmModule.forFeature([Application, User]),
    GatewayModule,
  ],
  providers: [ApplicationService, AbiService],
  controllers: [ApplicationController],
})
export class ApplicationModule {}
