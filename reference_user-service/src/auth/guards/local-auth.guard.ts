import {
  BadRequestException,
  ExecutionContext,
  HttpException,
  HttpStatus,
  Injectable,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { LoginUserDto } from '../../user/dto/user.dto';
import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';

@Injectable()
export class LocalAuthGuard extends AuthGuard('local') {
  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const body = request.body;
    // Convert plain request body to AdminLoginDto instance
    const userLoginDto = plainToInstance(LoginUserDto, body);
    // Validate the AdminLoginDto instance
    const errors = await validate(userLoginDto);
    if (errors.length > 0) {
      const errorMessages = errors
        .map((error) => Object.values(error.constraints))
        .join(', ');
      throw new BadRequestException({
        error: true,
        statusCode: HttpStatus.BAD_REQUEST,
        message: errorMessages,
      });
    }
    const result = (await super.canActivate(context)) as boolean;
    return result;
  }
}
