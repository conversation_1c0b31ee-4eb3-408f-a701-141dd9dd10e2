import { Request } from 'express';
import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { UserService } from '../../user/user.service';
import { CommonMessage } from '../../CommonMessages/CommonMessages';
import { formatDate } from 'src/utils/common.service';

declare module 'express' {
  interface Request {
    email: string;
  }
}
@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(private readonly userService: UserService) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: process.env.JWT_SECRET,
    });
  }

  async validate(request: Request) {
    const user = await this.userService.findUser(request.email);
    if (!user.isActive) {
      throw new HttpException(
        {
          error: true,
          message: CommonMessage.UserBlockedUntil(
            formatDate(user.blockedUntil),
          ),
          statusCode: HttpStatus.BAD_REQUEST,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
    if (!user) {
      throw new HttpException(
        {
          error: true,
          message: CommonMessage.InvalidCred,
          statusCode: HttpStatus.BAD_REQUEST,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
    return user;
  }
}
