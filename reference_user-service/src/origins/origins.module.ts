import { Modu<PERSON> } from '@nestjs/common';
import { OriginsService } from './origins.service';
import { Origins } from './entities/origins.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { Application } from '../application/entities/application.entity';
import { Bundler } from '../bundler/entities/bundler.entity';
import { OriginsController } from './origins.controller';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    // forwardRef(() => AuthModule),
    TypeOrmModule.forFeature([Origins, Application, Bundler]),
  ],
  providers: [OriginsService],
  exports: [OriginsService],
  controllers: [OriginsController],
})
export class OriginsModule {}
