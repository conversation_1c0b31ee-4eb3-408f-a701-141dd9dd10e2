# ChatAI Files Checklist - Complete File List

This document provides a complete checklist of all files that need to be copied or modified for ChatAI integration.

## 📁 **COMPLETE NEW FILES TO COPY**

### 1. **Core ChatAI Module** ✅
```
src/chatAi/chatAi.module.ts
src/chatAi/chatAi.controller.ts  
src/chatAi/chatAi.service.ts
```

### 2. **DTOs (Data Transfer Objects)** ✅
```
src/chatAi/dto/chatAi.dto.ts
```
**Contains**: All request/response DTOs for ChatAI APIs
- CreateChatAiDto, UpdateChatAiDto, FetchChatAisDto
- CreateDocumentDto, UpdateDocumentDto, FetchDocumentsDto, FetchDocumentContentDto
- CreateChatMessageDto, ChatQueryDto
- RemoveDocumentDto, RemoveChatAiDto

### 3. **Database Entities** ✅
```
src/chatAi/entities/chatAi.entity.ts
src/chatAi/entities/document.entity.ts
src/chatAi/entities/message.entity.ts
src/chatAi/entities/creditUsage.entity.ts
```

### 4. **Utility Files** ✅
```
src/utils/common.service.ts
```
**Contains**: Pagination helper function

## 📝 **FILES TO MODIFY**

### 1. **App Module** ⚠️ MODIFY
**File**: `src/app.module.ts`
**Change**: Add ChatAiModule import
```typescript
import { ChatAiModule } from './chatAi/chatAi.module';

@Module({
  imports: [
    // ... existing imports
    ChatAiModule,
  ],
})
```

### 2. **Application Entity** ⚠️ MODIFY  
**File**: `src/application/entities/application.entity.ts`
**Change**: Add ChatAI relationship
```typescript
import { ChatAi } from '../../chatAi/entities/chatAi.entity';

@OneToMany(() => ChatAi, (chatAi) => chatAi.app)
chatAis: ChatAi[];
```

### 3. **User Entity** ⚠️ MODIFY
**File**: `src/user/entities/user.entity.ts`  
**Change**: Add ChatAI relationship
```typescript
import { ChatAi } from '../../chatAi/entities/chatAi.entity';

@OneToMany(() => ChatAi, (chatAi) => chatAi.user)
chatAis: ChatAi[];
```

### 4. **Package Dependencies** ⚠️ MODIFY
**File**: `package.json`
**Add Dependencies**:
```json
{
  "dependencies": {
    "multer": "^1.4.5-lts.1",
    "node-fetch": "^2.6.7"
  },
  "devDependencies": {
    "@types/multer": "^1.4.7", 
    "@types/node-fetch": "^2.6.4"
  }
}
```

### 5. **Common Messages** ⚠️ MODIFY
**File**: `src/CommonMessages/CommonMessages.ts`
**Add**: ChatAI-specific message functions
```typescript
export class AppMessage {
  static ServiceCreated = (service: string) => `${service} created successfully`;
  static ServiceUpdated = (service: string) => `${service} updated successfully`;
  static ServiceDeleted = (service: string) => `${service} deleted successfully`;
  static ServiceFetched = (service: string) => `${service} fetched successfully`;
  static AppNotFound = () => 'Application not found or access denied';
  static DocumentNotFound = () => 'Document not found or access denied';
  static InsufficientCredits = () => 'Insufficient credits for this operation';
}
```

### 6. **Environment Configuration** ⚠️ MODIFY
**File**: `.env`
**Add Variables**:
```env
# ChatAI Configuration
CHATAI_SDK_URL=http://localhost:3001
INTERNAL_API_KEY=chatai-internal-2024

# File Upload Configuration  
MAX_FILE_SIZE=20971520
ALLOWED_FILE_TYPES=pdf,doc,docx,txt,md

# Credit System
DEFAULT_FREE_CREDITS=100
DEFAULT_PRO_CREDITS=10000
```

## 📊 **DATABASE MIGRATIONS NEEDED**

### Create Migration Files:
```
src/migrations/XXXX-create-chat-ai-tables.ts
src/migrations/XXXX-add-chat-ai-indexes.ts
```

### Tables to Create:
1. **chat_ai_projects** - Main ChatAI configurations
2. **chat_ai_documents** - Document storage and metadata  
3. **chat_ai_messages** - Chat conversation history
4. **chat_ai_credit_usage** - Credit usage tracking
5. **chat_ai_api_transactions** - API call logging

## 🔍 **VERIFICATION CHECKLIST**

### ✅ **Files Copied**
- [ ] All 4 core ChatAI module files
- [ ] All 4 entity files  
- [ ] DTO file with all classes
- [ ] Utility files

### ✅ **Files Modified**
- [ ] app.module.ts - ChatAiModule imported
- [ ] application.entity.ts - ChatAI relationship added
- [ ] user.entity.ts - ChatAI relationship added  
- [ ] package.json - Dependencies added
- [ ] CommonMessages.ts - ChatAI messages added
- [ ] .env - Environment variables added

### ✅ **Database Setup**
- [ ] Migration files created
- [ ] Database migrations run
- [ ] All 5 tables created
- [ ] Indexes created for performance

### ✅ **Dependencies Installed**
- [ ] `npm install` run after package.json changes
- [ ] multer and node-fetch installed
- [ ] TypeScript types installed

## 🚀 **POST-MERGE TESTING**

### API Endpoints to Test:
```bash
# ChatAI Project Management
POST   /users/app/chatai/create
GET    /users/app/chatai/get-chatais
PATCH  /users/app/chatai/update-chatai
DELETE /users/app/chatai/remove-chatai

# Document Management  
POST   /users/app/chatai/upload-document
GET    /users/app/chatai/get-documents
GET    /users/app/chatai/get-document-content
DELETE /users/app/chatai/remove-document

# Chat Functionality
POST   /users/app/chatai/create-message
GET    /users/app/chatai/get-messages
POST   /users/app/chatai/chat-query

# Integration
GET    /users/app/key-validator
```

## ⚠️ **CRITICAL NOTES**

1. **Credit System**: Currently disabled (lines 571-581 in chatAi.service.ts)
2. **File Storage**: Creates `uploads/` directory automatically
3. **Integration**: Requires ChatAI-SDK-Clean service running on port 3001
4. **Security**: All endpoints require JWT authentication
5. **File Limits**: 20MB max file size, specific file types only

---

**Total Files**: 8 new files + 6 modified files + 2 migration files = **16 files total**
