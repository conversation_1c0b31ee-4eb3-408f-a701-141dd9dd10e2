import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy } from 'passport-local';
import { CommonMessage } from '../../CommonMessages/CommonMessages';
import { AuthService } from '../auth.service';

@Injectable()
export class LocalStrategy extends PassportStrategy(Strategy) {
  constructor(private readonly authService: AuthService) {
    super({
      usernameField: 'email',
      passwordField: 'password',
    });
  }

  async validate(email: string, password: string): Promise<any> {
    const user = await this.authService.validateUser(email, password);
    if (!user) {
      throw new HttpException(
        {
          error: true,
          message: CommonMessage.InvalidCred,
          statusCode: HttpStatus.BAD_REQUEST,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
    if (user && !user.isEmailVerified) {
      throw new HttpException(
        {
          error: true,
          message: CommonMessage.EmailNotVerified,
          statusCode: HttpStatus.BAD_REQUEST,
          result: { email: user.email },
        },
        HttpStatus.BAD_REQUEST,
      );
    }
    if (user && !user.isActive) {
      throw new HttpException(
        {
          error: true,
          message: CommonMessage.UserBlocked,
          statusCode: HttpStatus.BAD_REQUEST,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
    return user;
  }
}
