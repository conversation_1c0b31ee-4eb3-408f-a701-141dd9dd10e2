import { Application } from '../../application/entities/application.entity';
import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedColumn,
  <PERSON><PERSON><PERSON><PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  OneToOne,
} from 'typeorm';

@Entity()
export class Bundler {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ nullable: false })
  bundlerName: string;

  @Column({ nullable: false, default: true })
  isActive: boolean;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt: Date;

  @OneToOne(() => Application, (app) => app.bundler, {
    cascade: ['insert', 'update', 'remove'], // Cascades on insert, update, and remove
    onDelete: 'CASCADE', // Cascades delete operations
  })
  @JoinColumn()
  app: Application;
}
