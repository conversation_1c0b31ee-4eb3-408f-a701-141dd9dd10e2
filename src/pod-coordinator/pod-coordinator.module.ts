import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PodCoordinator } from './entities/pod-coordinator.entity';
import { PodCoordinatorService } from './pod-coordinator.service';

@Module({
  imports: [TypeOrmModule.forFeature([PodCoordinator])],
  providers: [PodCoordinatorService],
  exports: [PodCoordinatorService],
})
export class PodCoordinatorModule {}
