export class CommonMessage {
  public static readonly Welcome = 'Welcome to Abstraxn backend';
  public static readonly Sucess = 'Successfully processed the request';
  public static readonly FetchSucess = 'Successfully fetched the data';
  public static readonly AlreadyCreated = 'User already exists';
  public static readonly UserNotFound = 'User not found';
  public static readonly EmailNotExist = "Email doesn't exist";
  public static readonly RegisterationSuccess = 'User created successfully';
  public static readonly AlreadyInUse = 'Username not available';
  public static readonly UsernameAvailable = 'Username is available';
  public static readonly InvalidCred = 'Invalid credentials';
  public static readonly InvalidCredLimit = (limits) =>
    `Invalid credentials, You have only ${limits} ${limits <= 1 ? 'attempt' : 'attempts'} left!`;
  public static readonly EmailNotVerified = 'Please verify your email';
  public static readonly UserBlocked =
    'Account blocked, Please contact with support';
  public static readonly UserBlockedUntil = (until) =>
    `Account blocked until ${until}`;
  public static readonly LoginSuccessfully = 'Login Successfully!';
  public static readonly InvalidOldPassword = 'Invalid old password';
  public static readonly SameOldAndNew =
    'Old and new password must be different';
  public static readonly PasswordChanged = 'Password changed successfully!';
  public static readonly InternalError = 'Internal Server Error';
  public static readonly NoDataFound = 'No data found';
  public static readonly MailSent = (field) =>
    `${field} verification mail sent to your email address`;
  public static readonly InvalidContractAddress = 'Contract address not valid';
  public static readonly AbiFound = 'ABI obtained successfully!';
  public static readonly InvalidABI = 'ABI is not valid';
  public static readonly InvalidABIMethods = 'ABI methods do not match';
}

export class UserMessage {
  public static readonly MailError = 'Failed to send the email';
  public static readonly Invalid = (field) => `Invalid ${field} request`;
  public static readonly SaveError = 'Failed to save the record';
  public static readonly LinkExpired = (field) => `${field} link has expired`;
  public static readonly OldLinkValid = (field) =>
    `Previous sent link to your email is still valid, Please try that link or try resend after ${field}`;
  public static readonly AlreadyVerified = 'User email already verified!';
  public static readonly UpdateSuccess = 'User updated successfully';
  public static readonly VerifiedSuccess = 'User email verified successfully!';
  public static readonly ResetSuccess = 'Password reset successfully!';
  public static readonly InvalidPlan = 'Please provide valid plan name';
  public static readonly AlreadyEnrolledPlan =
    'You have already enrolled with the same plan';
  public static readonly NotAllowedToCreateApp =
    'Please enroll any plan to register new app';
  public static readonly EnrolledPlan = (name: string = 'free') =>
    `Successfully enrolled you with ${name} plan`;
}

export class AppMessage {
  public static readonly InvalidChainId = 'Please provide valid chain id';
  public static readonly InvalidMode = 'Please provide valid mode';
  public static readonly AppCreated = (type = 'App') =>
    `${type} created successfully!`;
  public static readonly AlreadyCreated = (feature = 'App') =>
    `${feature} already created with same name`;
  public static readonly GasSetupSuccess = 'Gas tank setup successfully!';
  public static readonly GasSetupAlready = 'Gas tank already setup';
  public static readonly SignatureNotMatch =
    'Signature is not matched with funding wallet';
  public static readonly AppsFetched = 'Apps fetched successfully!';
  public static readonly AppFetched = 'App fetched successfully!';
  public static readonly AppNotFound = (type = 'App') =>
    `${type} not found for the user`;
  public static readonly AppLimitExceed = (limit: string) =>
    `You can only create up to ${limit} apps`;
  public static readonly AppAlreadySetSame = (feature: string) =>
    `${feature} already set to the provided value`;
  public static readonly AppUpdated = (featured = 'App updated') =>
    `${featured} successfully!`;
  public static readonly OriginAlreadyAdded =
    'Origin already added in this app';
  public static readonly OriginLimitExceed =
    'You can only add up to 10 origins';
  public static readonly OriginAdded = 'Origin added successfully!';
  public static readonly OriginNotFound = 'Origin not found';
  public static readonly FeatureRemoved = (feature = 'Origin') =>
    `${feature} removed successfully!`;
  public static readonly ContractNotSupported =
    "Your app's blockchain is not supported for obtaining the ABI";
  public static readonly ContarctAlreadyCreated = (feature = 'name') =>
    `Smart contract already whitelisted with same ${feature}`;
  public static readonly ContractCreated =
    'Smart contract whitelisted successfully!';
  public static readonly ContractUpdated =
    'Whitelisted smart contract updated successfully!';
  public static readonly ContractNotFound =
    'Smart contract not found for the user';
  public static readonly ContractRemoved =
    'Whitelisted smart contract removed successfully!';
  public static readonly ContractsFetched =
    'Smart contracts fetched successfully!';
  public static readonly OriginNotWhitelisted = (
    origin: string,
    service: string,
  ) => `Requesting ${origin} is not whitelisted in your ${service}`;
  public static readonly AlreadySetup = (service = 'service') =>
    `You have already set up the ${service} for this app.`;
  public static readonly SameValues =
    'The provided values are already the same.';
  public static readonly ServiceFetched = (service) =>
    `${service} fetched successfully`;
  public static readonly AppIsInactive = 'App is inactive!';
  public static readonly RemoveError = (feature = 'App') =>
    `Failed to remove ${feature}!`;
  public static readonly INVALID_API_KEY = 'Invalid API key';
  public static readonly ApiNotActive = 'API key is not active';
  public static readonly ChainNotAllowed = (chainId) =>
    `Api key is not allowed for this ${chainId} chain id`;
  public static readonly INVALID_SERVICE_TYPE = 'Please provide service type';
}

export class TransactionMessage {
  public static readonly NOT_FOUND = 'No such transaction found!';
  public static readonly FOUND = 'Transaction details fetched successfully';
  public static readonly LIST_FOUND = (type = 'Deposit') =>
    `${type} transactions fetched successfully`;
  public static readonly PROCESSED_ALREADY = (type = 'Depsite') =>
    `${type} transaction already processed`;
  public static readonly UPDATE_ERROR = 'Failed to update the transaction';
  public static readonly UPDATE_SUCCESS =
    'Cryto transaction updated successfully!';
  public static readonly CREATE_SUCCESS = (type = 'Depsite') =>
    `${type} submitted successfully`;
  public static readonly IN_PROGRESS = (type = 'Depsite') =>
    `Your ${type} is in progress,Please wait!`;
  public static readonly PROCESS_SUCCESS = (type = 'Depsite') =>
    `${type} processed successfully`;
  public static readonly CREATE_ERROR = 'Failed to create the transaction';
  public static readonly INVALID_TYPE = 'Please provide valid transaction type';
}
