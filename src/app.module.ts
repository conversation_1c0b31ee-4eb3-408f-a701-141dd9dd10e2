import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { UserModule } from './user/user.module';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from './user/entities/user.entity';
import { MailModule } from './mail/mail.module';
import { PaymasterModule } from './paymaster/paymaster.module';
import {
  Paymaster,
  SmartContract,
} from './paymaster/entities/paymaster.entity';
import { GatewayService } from './gateway/gateway.service';
import { GatewayModule } from './gateway/gateway.module';
import { HttpModule } from '@nestjs/axios';
import { TransactionModule } from './transaction/transaction.module';
import { Transaction } from './transaction/entity/transaction.entity';
import { Bundler } from './bundler/entities/bundler.entity';
import { SocketService } from './socket/server/socket.service';
import { SocketModule } from './socket/server/socket.module';
import { AuthModule } from './auth/auth.module';
import { ApplicationModule } from './application/application.module';
import { Application } from './application/entities/application.entity';
import { OriginsModule } from './origins/origins.module';
import { BundlerModule } from './bundler/bundler.module';
import { Origins } from './origins/entities/origins.entity';
import { SecurityHeadersMiddleware } from './middleware/middleware.security';
import { AppRelayer } from './appRelayer/entities/appRelayer.entity';
import { AppRelayerModule } from './appRelayer/appRelayer.module';
import { ScheduleModule } from '@nestjs/schedule';
import { EventHandlingModule } from './eventHandling/eventHandler.module';
import { BlockNumber } from './eventHandling/entities/blockNumber.entity';
import { NotificationModule } from './notification/notification.module';
import { RelayersTransaction } from './appRelayer/entities/transaction.entity';
import { PodCoordinatorModule } from './pod-coordinator/pod-coordinator.module';
import { PodCoordinator } from './pod-coordinator/entities/pod-coordinator.entity';
import { ChatAiModule } from './chatAi/chatAi.module';
import { ChatAi } from './chatAi/entities/chatAi.entity';
import { ChatAiDocument } from './chatAi/entities/document.entity';
import { ChatAiMessage } from './chatAi/entities/message.entity';
import { ChatAiCreditUsage } from './chatAi/entities/credit-usage.entity';
import { ChatAiApiTransaction } from './chatAi/entities/transaction.entity';
@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    TypeOrmModule.forRoot({
      type: 'postgres',
      host: process.env.POSTGRES_HOST || 'localhost',
      port: parseInt(process.env.POSTGRES_PORT, 10) || 5432,
      username: process.env.POSTGRES_USER || 'postgres',
      password: process.env.POSTGRES_PASSWORD || 'postgres',
      database: process.env.POSTGRES_DB || 'abstraxn',
      entities: [
        User,
        Application,
        Paymaster,
        Transaction,
        RelayersTransaction,
        SmartContract,
        Bundler,
        Origins,
        AppRelayer,
        BlockNumber,
        PodCoordinator,
        // ChatAI Entities
        ChatAi,
        ChatAiDocument,
        ChatAiMessage,
        ChatAiCreditUsage,
        ChatAiApiTransaction,
      ],
      synchronize: true,
    }),
    UserModule,
    MailModule,
    ApplicationModule,
    BundlerModule,
    PaymasterModule,
    GatewayModule,
    ChatAiModule,
    HttpModule.register({
      timeout: 5000,
    }),
    TransactionModule,
    SocketModule,
    AuthModule,
    OriginsModule,
    AppRelayerModule,
    EventHandlingModule,
    NotificationModule,
    PodCoordinatorModule,
    ScheduleModule.forRoot(),
  ],
  controllers: [AppController],
  providers: [AppService, GatewayService, SocketService],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(
        // EscapeXssMiddleware,
        SecurityHeadersMiddleware,
      )
      .forRoutes('*');
  }
}
