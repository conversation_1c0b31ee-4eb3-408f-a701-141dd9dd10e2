import { Controller, Post, Body, Get, Query, Delete } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { GatewayService } from './gateway.service';
import {
  AddGatewayConsumerDto,
  GateWayConsumerRateLimit,
  GateWayConsumerUsername,
  GateWayServiceName,
  GateWayConsumerApiKey,
} from './gateway.dto';

@ApiTags('gateway')
@Controller('gateway')
export class GatewayController {
  constructor(private readonly gatewayService: GatewayService) {}

  @Post('add-consumer')
  async addConsumer(@Body() addConsumerPayload: AddGatewayConsumerDto) {
    return this.gatewayService.createConsumer(addConsumerPayload);
  }

  @Post('assign-key')
  async assignKey(@Body() assignKeyPayload: GateWayConsumerUsername) {
    return this.gatewayService.assignConsumerkey(assignKeyPayload);
  }

  @Post('set-rate-limit')
  async setRateLimit(@Body() setRateLimitPayload: GateWayConsumerRateLimit) {
    return this.gatewayService.setConsumerRateLimit(setRateLimitPayload);
  }

  @Get('get-service-by-name')
  async getService(@Query() getServicePayload: GateWayServiceName) {
    return this.gatewayService.getServiceByName(getServicePayload);
  }

  @Delete('delete-consumer-key')
  async deleteApiKey(@Query() deleteKeyPayload: GateWayConsumerApiKey) {
    return this.gatewayService.deleteConsumerkey(deleteKeyPayload);
  }
}
