import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { GatewayService } from './gateway.service';
// import { GatewayController } from './gateway.controller';

@Module({
  imports: [
    HttpModule.register({
      timeout: 5000,
    }),
  ],
  providers: [GatewayService],
  // controllers: [GatewayController],
  exports: [GatewayService],
})
export class GatewayModule {}
