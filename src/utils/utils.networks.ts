export enum ChainId {
  MAINNET = 1,
  GOERLI = 5,
  SEPOLIA = 11155111,
  HOLESKY = 17000,
  POLYGON_MUMBAI = 80001,
  POLYGON_AMOY = 80002,
  POLYGON_MAINNET = 137,
  BSC_TESTNET = 97,
  BSC_MAINNET = 56,
  BERACHAIN_BEPOLIA = 80069,
  SKYMARVEL_TESTNET = 32384,
  SEI_TESTNET = 1328,
  SEI_MAINNET = 1329,
  POLYGON_ZKEVM_TESTNET = 1442,
  POLYGON_ZKEVM_MAINNET = 1101,
  ARBITRUM_GOERLI_TESTNET = 421613,
  ARBITRUM_ONE_MAINNET = 42161,
  ARBITRUM_NOVA_MAINNET = 42170,
  OPTIMISM_MAINNET = 10,
  OPTIMISM_GOERLI_TESTNET = 420,
  AVALANCHE_MAINNET = 43114,
  AVALANCHE_TESTNET = 43113,
  MOONBEAM_MAINNET = 1284,
  BASE_GOERLI_TESTNET = 84531,
  BASE_SEPOLIA_TESTNET = 84532,
  BASE_MAINNET = 8453,
  LINEA_TESTNET = 59140,
  LINEA_MAINNET = 59144,
  MANTLE_MAINNET = 5000,
  MANTLE_TESTNET = 5001,
  OPBNB_MAINNET = 204,
  OPBNB_TESTNET = 5611,
  ASTAR_MAINNET = 592,
  ASTAR_TESTNET = 81,
  CHILIZ_MAINNET = 88888,
  CHILIZ_TESTNET = 88882,
  GANACHE = 1337,
}

export const RPC_PROVIDER_URLS: { [key in ChainId]?: string } = {
  [ChainId.MAINNET]:
    'https://chain.instanodes.io/eth-mainnet/?apikey=glddVsF3ltqQLez5QCeUsZL5VWehOeeI',
  [ChainId.GOERLI]: 'https://rpc.ankr.com/eth_goerli',
  [ChainId.SEPOLIA]:
    'https://chain.instanodes.io/eth-testnet/?apikey=KT87hH9M6fbRQi6eX5IibfkkcMkTO81B',
  [ChainId.HOLESKY]:
    'https://summer-falling-wave.ethereum-holesky.quiknode.pro/b7669cd4b44a9ec4075b321b088d1b3d3341073f',
  [ChainId.POLYGON_MUMBAI]:
    'https://polygon-mumbai.g.alchemy.com/v2/********************************',
  [ChainId.POLYGON_AMOY]: 'https://rpc-amoy.polygon.technology',
  [ChainId.POLYGON_MAINNET]:
    'https://chain.instanodes.io/polygon-mainnet/?apikey=********************************',
  [ChainId.BSC_TESTNET]:
    'https://chain.instanodes.io/bsc-testnet/?apikey=8LsGgT2GZq0uJxh8QzhXf1G1XaBHagmC',
  [ChainId.BSC_MAINNET]: 'https://rpc.ankr.com/bsc',
  [ChainId.BERACHAIN_BEPOLIA]: 'https://bepolia.rpc.berachain.com',
  [ChainId.SKYMARVEL_TESTNET]: 'https://rpc.testnetv2.explorer.skymarvel.io',
  [ChainId.SEI_TESTNET]: 'https://evm-rpc-testnet.sei-apis.com',
  [ChainId.SEI_MAINNET]: 'https://evm-rpc.sei-apis.com',
  [ChainId.POLYGON_ZKEVM_TESTNET]: 'https://rpc.public.zkevm-test.net',
  [ChainId.POLYGON_ZKEVM_MAINNET]: 'https://rpc.ankr.com/polygon_zkevm',
  [ChainId.ARBITRUM_GOERLI_TESTNET]: 'https://goerli-rollup.arbitrum.io/rpc',
  [ChainId.ARBITRUM_ONE_MAINNET]: 'https://rpc.ankr.com/arbitrum',
  [ChainId.ARBITRUM_NOVA_MAINNET]: 'https://nova.arbitrum.io/rpc',
  [ChainId.OPTIMISM_MAINNET]: 'https://mainnet.optimism.io',
  [ChainId.OPTIMISM_GOERLI_TESTNET]: 'https://goerli.optimism.io',
  [ChainId.AVALANCHE_MAINNET]: 'https://api.avax.network/ext/bc/C/rpc',
  [ChainId.AVALANCHE_TESTNET]: 'https://api.avax-test.network/ext/bc/C/rpc',
  [ChainId.MOONBEAM_MAINNET]: 'https://rpc.api.moonbeam.network',
  [ChainId.BASE_GOERLI_TESTNET]: 'https://goerli.base.org',
  [ChainId.BASE_MAINNET]: 'https://developer-access-mainnet.base.org',
  [ChainId.BASE_SEPOLIA_TESTNET]: 'https://sepolia.base.org',
  [ChainId.LINEA_TESTNET]: 'https://rpc.goerli.linea.build',
  [ChainId.LINEA_MAINNET]: 'https://rpc.linea.build',
  [ChainId.MANTLE_MAINNET]: 'https://rpc.mantle.xyz',
  [ChainId.MANTLE_TESTNET]: 'https://rpc.testnet.mantle.xyz',
  [ChainId.OPBNB_MAINNET]: 'https://opbnb-mainnet-rpc.bnbchain.org',
  [ChainId.OPBNB_TESTNET]: 'https://opbnb-testnet-rpc.bnbchain.org',
  [ChainId.ASTAR_MAINNET]: 'https://evm.astar.network',
  [ChainId.ASTAR_TESTNET]: 'https://evm.shibuya.astar.network',
  [ChainId.CHILIZ_MAINNET]: 'https://rpc.ankr.com/chiliz',
  [ChainId.CHILIZ_TESTNET]: 'https://spicy-rpc.chiliz.com',
};
